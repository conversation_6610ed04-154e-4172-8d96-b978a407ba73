{"servers": {"github": {"type": "http", "url": "https://api.githubcopilot.com/mcp/", "gallery": true}, "playwright": {"type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest"], "gallery": true}, "huggingface": {"type": "http", "url": "https://hf.co/mcp", "gallery": true}, "deepwiki": {"type": "http", "url": "https://mcp.deepwiki.com/sse", "gallery": true}, "markitdown": {"type": "stdio", "command": "uvx", "args": ["markitdown-mcp"], "gallery": true}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"], "gallery": true}, "sequentialthinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"], "gallery": true}, "memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory@latest"], "env": {"MEMORY_FILE_PATH": "${input:memory_file_path}"}, "gallery": true}, "mcp-feedback-collector": {"type": "stdio", "command": "/home/<USER>/mcp-venv/bin/python", "args": ["-m", "mcp_feedback_collector"], "env": {"PYTHONIOENCODING": "utf-8", "MCP_DIALOG_TIMEOUT": "600"}, "gallery": true}, "podman": {"type": "stdio", "command": "npx", "args": ["-y", "podman-mcp-server@latest"], "gallery": true}}, "inputs": [{"id": "memory_file_path", "type": "promptString", "description": "Path to the memory storage file", "password": false}]}